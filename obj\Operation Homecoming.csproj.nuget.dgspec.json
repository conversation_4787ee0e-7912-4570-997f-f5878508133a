{"format": 1, "restore": {"D:\\Projects\\Rimworld Modding\\Operation Homecoming\\Operation Homecoming.csproj": {}}, "projects": {"D:\\Projects\\Rimworld Modding\\Operation Homecoming\\Operation Homecoming.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\Rimworld Modding\\Operation Homecoming\\Operation Homecoming.csproj", "projectName": "Operation Homecoming", "projectPath": "D:\\Projects\\Rimworld Modding\\Operation Homecoming\\Operation Homecoming.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\Rimworld Modding\\Operation Homecoming\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net472": {"projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net472": {"dependencies": {"Krafs.Publicizer": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.3.0, )"}, "Krafs.Rimworld.Ref": {"target": "Package", "version": "[1.5.4409, )"}, "Lib.Harmony": {"target": "Package", "version": "[2.3.6, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}}
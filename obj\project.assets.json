{"version": 3, "targets": {".NETFramework,Version=v4.7.2": {"Krafs.Publicizer/2.3.0": {"type": "package", "contentFiles": {"contentfiles/cs/any/Publicizer/IgnoresAccessChecksToAttribute.cs": {"buildAction": "Compile", "codeLanguage": "cs", "copyToOutput": false}}, "build": {"build/Krafs.Publicizer.props": {}, "build/Krafs.Publicizer.targets": {}}}, "Krafs.Rimworld.Ref/1.5.4409": {"type": "package", "compile": {"ref/net472/Assembly-CSharp-firstpass.dll": {}, "ref/net472/Assembly-CSharp.dll": {}, "ref/net472/ISharpZipLib.dll": {}, "ref/net472/Mono.Posix.dll": {}, "ref/net472/Mono.Security.dll": {}, "ref/net472/NAudio.dll": {}, "ref/net472/NVorbis.dll": {}, "ref/net472/System.ComponentModel.Composition.dll": {}, "ref/net472/System.Configuration.dll": {}, "ref/net472/System.Core.dll": {}, "ref/net472/System.Runtime.Serialization.dll": {}, "ref/net472/System.Runtime.dll": {}, "ref/net472/System.Security.dll": {}, "ref/net472/System.ServiceModel.Internals.dll": {}, "ref/net472/System.Xml.Linq.dll": {}, "ref/net472/System.Xml.dll": {}, "ref/net472/System.dll": {}, "ref/net472/Unity.Burst.Unsafe.dll": {}, "ref/net472/Unity.Burst.dll": {}, "ref/net472/Unity.Mathematics.dll": {}, "ref/net472/Unity.MemoryProfiler.dll": {}, "ref/net472/Unity.TextMeshPro.dll": {}, "ref/net472/UnityEngine.AIModule.dll": {}, "ref/net472/UnityEngine.ARModule.dll": {}, "ref/net472/UnityEngine.AccessibilityModule.dll": {}, "ref/net472/UnityEngine.AndroidJNIModule.dll": {}, "ref/net472/UnityEngine.AnimationModule.dll": {}, "ref/net472/UnityEngine.AssetBundleModule.dll": {}, "ref/net472/UnityEngine.AudioModule.dll": {}, "ref/net472/UnityEngine.ClothModule.dll": {}, "ref/net472/UnityEngine.ClusterInputModule.dll": {}, "ref/net472/UnityEngine.ClusterRendererModule.dll": {}, "ref/net472/UnityEngine.CoreModule.dll": {}, "ref/net472/UnityEngine.CrashReportingModule.dll": {}, "ref/net472/UnityEngine.DSPGraphModule.dll": {}, "ref/net472/UnityEngine.DirectorModule.dll": {}, "ref/net472/UnityEngine.GameCenterModule.dll": {}, "ref/net472/UnityEngine.GridModule.dll": {}, "ref/net472/UnityEngine.HotReloadModule.dll": {}, "ref/net472/UnityEngine.IMGUIModule.dll": {}, "ref/net472/UnityEngine.ImageConversionModule.dll": {}, "ref/net472/UnityEngine.InputLegacyModule.dll": {}, "ref/net472/UnityEngine.InputModule.dll": {}, "ref/net472/UnityEngine.JSONSerializeModule.dll": {}, "ref/net472/UnityEngine.LocalizationModule.dll": {}, "ref/net472/UnityEngine.ParticleSystemModule.dll": {}, "ref/net472/UnityEngine.PerformanceReportingModule.dll": {}, "ref/net472/UnityEngine.Physics2DModule.dll": {}, "ref/net472/UnityEngine.PhysicsModule.dll": {}, "ref/net472/UnityEngine.ProfilerModule.dll": {}, "ref/net472/UnityEngine.ScreenCaptureModule.dll": {}, "ref/net472/UnityEngine.SharedInternalsModule.dll": {}, "ref/net472/UnityEngine.SpriteMaskModule.dll": {}, "ref/net472/UnityEngine.SpriteShapeModule.dll": {}, "ref/net472/UnityEngine.StreamingModule.dll": {}, "ref/net472/UnityEngine.SubstanceModule.dll": {}, "ref/net472/UnityEngine.SubsystemsModule.dll": {}, "ref/net472/UnityEngine.TLSModule.dll": {}, "ref/net472/UnityEngine.TerrainModule.dll": {}, "ref/net472/UnityEngine.TerrainPhysicsModule.dll": {}, "ref/net472/UnityEngine.TextCoreModule.dll": {}, "ref/net472/UnityEngine.TextRenderingModule.dll": {}, "ref/net472/UnityEngine.TilemapModule.dll": {}, "ref/net472/UnityEngine.UI.dll": {}, "ref/net472/UnityEngine.UIElementsModule.dll": {}, "ref/net472/UnityEngine.UIModule.dll": {}, "ref/net472/UnityEngine.UNETModule.dll": {}, "ref/net472/UnityEngine.UmbraModule.dll": {}, "ref/net472/UnityEngine.UnityAnalyticsModule.dll": {}, "ref/net472/UnityEngine.UnityConnectModule.dll": {}, "ref/net472/UnityEngine.UnityTestProtocolModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestAssetBundleModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestAudioModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestTextureModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestWWWModule.dll": {}, "ref/net472/UnityEngine.VFXModule.dll": {}, "ref/net472/UnityEngine.VRModule.dll": {}, "ref/net472/UnityEngine.VehiclesModule.dll": {}, "ref/net472/UnityEngine.VideoModule.dll": {}, "ref/net472/UnityEngine.WindModule.dll": {}, "ref/net472/UnityEngine.XRModule.dll": {}, "ref/net472/UnityEngine.dll": {}, "ref/net472/com.rlabrecque.steamworks.net.dll": {}, "ref/net472/mscorlib.dll": {}}}, "Lib.Harmony/2.3.6": {"type": "package", "compile": {"lib/net472/0Harmony.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net472/0Harmony.dll": {"related": ".pdb;.xml"}}}}, ".NETFramework,Version=v4.7.2/win": {"Krafs.Publicizer/2.3.0": {"type": "package", "contentFiles": {"contentfiles/cs/any/Publicizer/IgnoresAccessChecksToAttribute.cs": {"buildAction": "Compile", "codeLanguage": "cs", "copyToOutput": false}}, "build": {"build/Krafs.Publicizer.props": {}, "build/Krafs.Publicizer.targets": {}}}, "Krafs.Rimworld.Ref/1.5.4409": {"type": "package", "compile": {"ref/net472/Assembly-CSharp-firstpass.dll": {}, "ref/net472/Assembly-CSharp.dll": {}, "ref/net472/ISharpZipLib.dll": {}, "ref/net472/Mono.Posix.dll": {}, "ref/net472/Mono.Security.dll": {}, "ref/net472/NAudio.dll": {}, "ref/net472/NVorbis.dll": {}, "ref/net472/System.ComponentModel.Composition.dll": {}, "ref/net472/System.Configuration.dll": {}, "ref/net472/System.Core.dll": {}, "ref/net472/System.Runtime.Serialization.dll": {}, "ref/net472/System.Runtime.dll": {}, "ref/net472/System.Security.dll": {}, "ref/net472/System.ServiceModel.Internals.dll": {}, "ref/net472/System.Xml.Linq.dll": {}, "ref/net472/System.Xml.dll": {}, "ref/net472/System.dll": {}, "ref/net472/Unity.Burst.Unsafe.dll": {}, "ref/net472/Unity.Burst.dll": {}, "ref/net472/Unity.Mathematics.dll": {}, "ref/net472/Unity.MemoryProfiler.dll": {}, "ref/net472/Unity.TextMeshPro.dll": {}, "ref/net472/UnityEngine.AIModule.dll": {}, "ref/net472/UnityEngine.ARModule.dll": {}, "ref/net472/UnityEngine.AccessibilityModule.dll": {}, "ref/net472/UnityEngine.AndroidJNIModule.dll": {}, "ref/net472/UnityEngine.AnimationModule.dll": {}, "ref/net472/UnityEngine.AssetBundleModule.dll": {}, "ref/net472/UnityEngine.AudioModule.dll": {}, "ref/net472/UnityEngine.ClothModule.dll": {}, "ref/net472/UnityEngine.ClusterInputModule.dll": {}, "ref/net472/UnityEngine.ClusterRendererModule.dll": {}, "ref/net472/UnityEngine.CoreModule.dll": {}, "ref/net472/UnityEngine.CrashReportingModule.dll": {}, "ref/net472/UnityEngine.DSPGraphModule.dll": {}, "ref/net472/UnityEngine.DirectorModule.dll": {}, "ref/net472/UnityEngine.GameCenterModule.dll": {}, "ref/net472/UnityEngine.GridModule.dll": {}, "ref/net472/UnityEngine.HotReloadModule.dll": {}, "ref/net472/UnityEngine.IMGUIModule.dll": {}, "ref/net472/UnityEngine.ImageConversionModule.dll": {}, "ref/net472/UnityEngine.InputLegacyModule.dll": {}, "ref/net472/UnityEngine.InputModule.dll": {}, "ref/net472/UnityEngine.JSONSerializeModule.dll": {}, "ref/net472/UnityEngine.LocalizationModule.dll": {}, "ref/net472/UnityEngine.ParticleSystemModule.dll": {}, "ref/net472/UnityEngine.PerformanceReportingModule.dll": {}, "ref/net472/UnityEngine.Physics2DModule.dll": {}, "ref/net472/UnityEngine.PhysicsModule.dll": {}, "ref/net472/UnityEngine.ProfilerModule.dll": {}, "ref/net472/UnityEngine.ScreenCaptureModule.dll": {}, "ref/net472/UnityEngine.SharedInternalsModule.dll": {}, "ref/net472/UnityEngine.SpriteMaskModule.dll": {}, "ref/net472/UnityEngine.SpriteShapeModule.dll": {}, "ref/net472/UnityEngine.StreamingModule.dll": {}, "ref/net472/UnityEngine.SubstanceModule.dll": {}, "ref/net472/UnityEngine.SubsystemsModule.dll": {}, "ref/net472/UnityEngine.TLSModule.dll": {}, "ref/net472/UnityEngine.TerrainModule.dll": {}, "ref/net472/UnityEngine.TerrainPhysicsModule.dll": {}, "ref/net472/UnityEngine.TextCoreModule.dll": {}, "ref/net472/UnityEngine.TextRenderingModule.dll": {}, "ref/net472/UnityEngine.TilemapModule.dll": {}, "ref/net472/UnityEngine.UI.dll": {}, "ref/net472/UnityEngine.UIElementsModule.dll": {}, "ref/net472/UnityEngine.UIModule.dll": {}, "ref/net472/UnityEngine.UNETModule.dll": {}, "ref/net472/UnityEngine.UmbraModule.dll": {}, "ref/net472/UnityEngine.UnityAnalyticsModule.dll": {}, "ref/net472/UnityEngine.UnityConnectModule.dll": {}, "ref/net472/UnityEngine.UnityTestProtocolModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestAssetBundleModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestAudioModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestTextureModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestWWWModule.dll": {}, "ref/net472/UnityEngine.VFXModule.dll": {}, "ref/net472/UnityEngine.VRModule.dll": {}, "ref/net472/UnityEngine.VehiclesModule.dll": {}, "ref/net472/UnityEngine.VideoModule.dll": {}, "ref/net472/UnityEngine.WindModule.dll": {}, "ref/net472/UnityEngine.XRModule.dll": {}, "ref/net472/UnityEngine.dll": {}, "ref/net472/com.rlabrecque.steamworks.net.dll": {}, "ref/net472/mscorlib.dll": {}}}, "Lib.Harmony/2.3.6": {"type": "package", "compile": {"lib/net472/0Harmony.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net472/0Harmony.dll": {"related": ".pdb;.xml"}}}}, ".NETFramework,Version=v4.7.2/win-arm64": {"Krafs.Publicizer/2.3.0": {"type": "package", "contentFiles": {"contentfiles/cs/any/Publicizer/IgnoresAccessChecksToAttribute.cs": {"buildAction": "Compile", "codeLanguage": "cs", "copyToOutput": false}}, "build": {"build/Krafs.Publicizer.props": {}, "build/Krafs.Publicizer.targets": {}}}, "Krafs.Rimworld.Ref/1.5.4409": {"type": "package", "compile": {"ref/net472/Assembly-CSharp-firstpass.dll": {}, "ref/net472/Assembly-CSharp.dll": {}, "ref/net472/ISharpZipLib.dll": {}, "ref/net472/Mono.Posix.dll": {}, "ref/net472/Mono.Security.dll": {}, "ref/net472/NAudio.dll": {}, "ref/net472/NVorbis.dll": {}, "ref/net472/System.ComponentModel.Composition.dll": {}, "ref/net472/System.Configuration.dll": {}, "ref/net472/System.Core.dll": {}, "ref/net472/System.Runtime.Serialization.dll": {}, "ref/net472/System.Runtime.dll": {}, "ref/net472/System.Security.dll": {}, "ref/net472/System.ServiceModel.Internals.dll": {}, "ref/net472/System.Xml.Linq.dll": {}, "ref/net472/System.Xml.dll": {}, "ref/net472/System.dll": {}, "ref/net472/Unity.Burst.Unsafe.dll": {}, "ref/net472/Unity.Burst.dll": {}, "ref/net472/Unity.Mathematics.dll": {}, "ref/net472/Unity.MemoryProfiler.dll": {}, "ref/net472/Unity.TextMeshPro.dll": {}, "ref/net472/UnityEngine.AIModule.dll": {}, "ref/net472/UnityEngine.ARModule.dll": {}, "ref/net472/UnityEngine.AccessibilityModule.dll": {}, "ref/net472/UnityEngine.AndroidJNIModule.dll": {}, "ref/net472/UnityEngine.AnimationModule.dll": {}, "ref/net472/UnityEngine.AssetBundleModule.dll": {}, "ref/net472/UnityEngine.AudioModule.dll": {}, "ref/net472/UnityEngine.ClothModule.dll": {}, "ref/net472/UnityEngine.ClusterInputModule.dll": {}, "ref/net472/UnityEngine.ClusterRendererModule.dll": {}, "ref/net472/UnityEngine.CoreModule.dll": {}, "ref/net472/UnityEngine.CrashReportingModule.dll": {}, "ref/net472/UnityEngine.DSPGraphModule.dll": {}, "ref/net472/UnityEngine.DirectorModule.dll": {}, "ref/net472/UnityEngine.GameCenterModule.dll": {}, "ref/net472/UnityEngine.GridModule.dll": {}, "ref/net472/UnityEngine.HotReloadModule.dll": {}, "ref/net472/UnityEngine.IMGUIModule.dll": {}, "ref/net472/UnityEngine.ImageConversionModule.dll": {}, "ref/net472/UnityEngine.InputLegacyModule.dll": {}, "ref/net472/UnityEngine.InputModule.dll": {}, "ref/net472/UnityEngine.JSONSerializeModule.dll": {}, "ref/net472/UnityEngine.LocalizationModule.dll": {}, "ref/net472/UnityEngine.ParticleSystemModule.dll": {}, "ref/net472/UnityEngine.PerformanceReportingModule.dll": {}, "ref/net472/UnityEngine.Physics2DModule.dll": {}, "ref/net472/UnityEngine.PhysicsModule.dll": {}, "ref/net472/UnityEngine.ProfilerModule.dll": {}, "ref/net472/UnityEngine.ScreenCaptureModule.dll": {}, "ref/net472/UnityEngine.SharedInternalsModule.dll": {}, "ref/net472/UnityEngine.SpriteMaskModule.dll": {}, "ref/net472/UnityEngine.SpriteShapeModule.dll": {}, "ref/net472/UnityEngine.StreamingModule.dll": {}, "ref/net472/UnityEngine.SubstanceModule.dll": {}, "ref/net472/UnityEngine.SubsystemsModule.dll": {}, "ref/net472/UnityEngine.TLSModule.dll": {}, "ref/net472/UnityEngine.TerrainModule.dll": {}, "ref/net472/UnityEngine.TerrainPhysicsModule.dll": {}, "ref/net472/UnityEngine.TextCoreModule.dll": {}, "ref/net472/UnityEngine.TextRenderingModule.dll": {}, "ref/net472/UnityEngine.TilemapModule.dll": {}, "ref/net472/UnityEngine.UI.dll": {}, "ref/net472/UnityEngine.UIElementsModule.dll": {}, "ref/net472/UnityEngine.UIModule.dll": {}, "ref/net472/UnityEngine.UNETModule.dll": {}, "ref/net472/UnityEngine.UmbraModule.dll": {}, "ref/net472/UnityEngine.UnityAnalyticsModule.dll": {}, "ref/net472/UnityEngine.UnityConnectModule.dll": {}, "ref/net472/UnityEngine.UnityTestProtocolModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestAssetBundleModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestAudioModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestTextureModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestWWWModule.dll": {}, "ref/net472/UnityEngine.VFXModule.dll": {}, "ref/net472/UnityEngine.VRModule.dll": {}, "ref/net472/UnityEngine.VehiclesModule.dll": {}, "ref/net472/UnityEngine.VideoModule.dll": {}, "ref/net472/UnityEngine.WindModule.dll": {}, "ref/net472/UnityEngine.XRModule.dll": {}, "ref/net472/UnityEngine.dll": {}, "ref/net472/com.rlabrecque.steamworks.net.dll": {}, "ref/net472/mscorlib.dll": {}}}, "Lib.Harmony/2.3.6": {"type": "package", "compile": {"lib/net472/0Harmony.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net472/0Harmony.dll": {"related": ".pdb;.xml"}}}}, ".NETFramework,Version=v4.7.2/win-x64": {"Krafs.Publicizer/2.3.0": {"type": "package", "contentFiles": {"contentfiles/cs/any/Publicizer/IgnoresAccessChecksToAttribute.cs": {"buildAction": "Compile", "codeLanguage": "cs", "copyToOutput": false}}, "build": {"build/Krafs.Publicizer.props": {}, "build/Krafs.Publicizer.targets": {}}}, "Krafs.Rimworld.Ref/1.5.4409": {"type": "package", "compile": {"ref/net472/Assembly-CSharp-firstpass.dll": {}, "ref/net472/Assembly-CSharp.dll": {}, "ref/net472/ISharpZipLib.dll": {}, "ref/net472/Mono.Posix.dll": {}, "ref/net472/Mono.Security.dll": {}, "ref/net472/NAudio.dll": {}, "ref/net472/NVorbis.dll": {}, "ref/net472/System.ComponentModel.Composition.dll": {}, "ref/net472/System.Configuration.dll": {}, "ref/net472/System.Core.dll": {}, "ref/net472/System.Runtime.Serialization.dll": {}, "ref/net472/System.Runtime.dll": {}, "ref/net472/System.Security.dll": {}, "ref/net472/System.ServiceModel.Internals.dll": {}, "ref/net472/System.Xml.Linq.dll": {}, "ref/net472/System.Xml.dll": {}, "ref/net472/System.dll": {}, "ref/net472/Unity.Burst.Unsafe.dll": {}, "ref/net472/Unity.Burst.dll": {}, "ref/net472/Unity.Mathematics.dll": {}, "ref/net472/Unity.MemoryProfiler.dll": {}, "ref/net472/Unity.TextMeshPro.dll": {}, "ref/net472/UnityEngine.AIModule.dll": {}, "ref/net472/UnityEngine.ARModule.dll": {}, "ref/net472/UnityEngine.AccessibilityModule.dll": {}, "ref/net472/UnityEngine.AndroidJNIModule.dll": {}, "ref/net472/UnityEngine.AnimationModule.dll": {}, "ref/net472/UnityEngine.AssetBundleModule.dll": {}, "ref/net472/UnityEngine.AudioModule.dll": {}, "ref/net472/UnityEngine.ClothModule.dll": {}, "ref/net472/UnityEngine.ClusterInputModule.dll": {}, "ref/net472/UnityEngine.ClusterRendererModule.dll": {}, "ref/net472/UnityEngine.CoreModule.dll": {}, "ref/net472/UnityEngine.CrashReportingModule.dll": {}, "ref/net472/UnityEngine.DSPGraphModule.dll": {}, "ref/net472/UnityEngine.DirectorModule.dll": {}, "ref/net472/UnityEngine.GameCenterModule.dll": {}, "ref/net472/UnityEngine.GridModule.dll": {}, "ref/net472/UnityEngine.HotReloadModule.dll": {}, "ref/net472/UnityEngine.IMGUIModule.dll": {}, "ref/net472/UnityEngine.ImageConversionModule.dll": {}, "ref/net472/UnityEngine.InputLegacyModule.dll": {}, "ref/net472/UnityEngine.InputModule.dll": {}, "ref/net472/UnityEngine.JSONSerializeModule.dll": {}, "ref/net472/UnityEngine.LocalizationModule.dll": {}, "ref/net472/UnityEngine.ParticleSystemModule.dll": {}, "ref/net472/UnityEngine.PerformanceReportingModule.dll": {}, "ref/net472/UnityEngine.Physics2DModule.dll": {}, "ref/net472/UnityEngine.PhysicsModule.dll": {}, "ref/net472/UnityEngine.ProfilerModule.dll": {}, "ref/net472/UnityEngine.ScreenCaptureModule.dll": {}, "ref/net472/UnityEngine.SharedInternalsModule.dll": {}, "ref/net472/UnityEngine.SpriteMaskModule.dll": {}, "ref/net472/UnityEngine.SpriteShapeModule.dll": {}, "ref/net472/UnityEngine.StreamingModule.dll": {}, "ref/net472/UnityEngine.SubstanceModule.dll": {}, "ref/net472/UnityEngine.SubsystemsModule.dll": {}, "ref/net472/UnityEngine.TLSModule.dll": {}, "ref/net472/UnityEngine.TerrainModule.dll": {}, "ref/net472/UnityEngine.TerrainPhysicsModule.dll": {}, "ref/net472/UnityEngine.TextCoreModule.dll": {}, "ref/net472/UnityEngine.TextRenderingModule.dll": {}, "ref/net472/UnityEngine.TilemapModule.dll": {}, "ref/net472/UnityEngine.UI.dll": {}, "ref/net472/UnityEngine.UIElementsModule.dll": {}, "ref/net472/UnityEngine.UIModule.dll": {}, "ref/net472/UnityEngine.UNETModule.dll": {}, "ref/net472/UnityEngine.UmbraModule.dll": {}, "ref/net472/UnityEngine.UnityAnalyticsModule.dll": {}, "ref/net472/UnityEngine.UnityConnectModule.dll": {}, "ref/net472/UnityEngine.UnityTestProtocolModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestAssetBundleModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestAudioModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestTextureModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestWWWModule.dll": {}, "ref/net472/UnityEngine.VFXModule.dll": {}, "ref/net472/UnityEngine.VRModule.dll": {}, "ref/net472/UnityEngine.VehiclesModule.dll": {}, "ref/net472/UnityEngine.VideoModule.dll": {}, "ref/net472/UnityEngine.WindModule.dll": {}, "ref/net472/UnityEngine.XRModule.dll": {}, "ref/net472/UnityEngine.dll": {}, "ref/net472/com.rlabrecque.steamworks.net.dll": {}, "ref/net472/mscorlib.dll": {}}}, "Lib.Harmony/2.3.6": {"type": "package", "compile": {"lib/net472/0Harmony.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net472/0Harmony.dll": {"related": ".pdb;.xml"}}}}, ".NETFramework,Version=v4.7.2/win-x86": {"Krafs.Publicizer/2.3.0": {"type": "package", "contentFiles": {"contentfiles/cs/any/Publicizer/IgnoresAccessChecksToAttribute.cs": {"buildAction": "Compile", "codeLanguage": "cs", "copyToOutput": false}}, "build": {"build/Krafs.Publicizer.props": {}, "build/Krafs.Publicizer.targets": {}}}, "Krafs.Rimworld.Ref/1.5.4409": {"type": "package", "compile": {"ref/net472/Assembly-CSharp-firstpass.dll": {}, "ref/net472/Assembly-CSharp.dll": {}, "ref/net472/ISharpZipLib.dll": {}, "ref/net472/Mono.Posix.dll": {}, "ref/net472/Mono.Security.dll": {}, "ref/net472/NAudio.dll": {}, "ref/net472/NVorbis.dll": {}, "ref/net472/System.ComponentModel.Composition.dll": {}, "ref/net472/System.Configuration.dll": {}, "ref/net472/System.Core.dll": {}, "ref/net472/System.Runtime.Serialization.dll": {}, "ref/net472/System.Runtime.dll": {}, "ref/net472/System.Security.dll": {}, "ref/net472/System.ServiceModel.Internals.dll": {}, "ref/net472/System.Xml.Linq.dll": {}, "ref/net472/System.Xml.dll": {}, "ref/net472/System.dll": {}, "ref/net472/Unity.Burst.Unsafe.dll": {}, "ref/net472/Unity.Burst.dll": {}, "ref/net472/Unity.Mathematics.dll": {}, "ref/net472/Unity.MemoryProfiler.dll": {}, "ref/net472/Unity.TextMeshPro.dll": {}, "ref/net472/UnityEngine.AIModule.dll": {}, "ref/net472/UnityEngine.ARModule.dll": {}, "ref/net472/UnityEngine.AccessibilityModule.dll": {}, "ref/net472/UnityEngine.AndroidJNIModule.dll": {}, "ref/net472/UnityEngine.AnimationModule.dll": {}, "ref/net472/UnityEngine.AssetBundleModule.dll": {}, "ref/net472/UnityEngine.AudioModule.dll": {}, "ref/net472/UnityEngine.ClothModule.dll": {}, "ref/net472/UnityEngine.ClusterInputModule.dll": {}, "ref/net472/UnityEngine.ClusterRendererModule.dll": {}, "ref/net472/UnityEngine.CoreModule.dll": {}, "ref/net472/UnityEngine.CrashReportingModule.dll": {}, "ref/net472/UnityEngine.DSPGraphModule.dll": {}, "ref/net472/UnityEngine.DirectorModule.dll": {}, "ref/net472/UnityEngine.GameCenterModule.dll": {}, "ref/net472/UnityEngine.GridModule.dll": {}, "ref/net472/UnityEngine.HotReloadModule.dll": {}, "ref/net472/UnityEngine.IMGUIModule.dll": {}, "ref/net472/UnityEngine.ImageConversionModule.dll": {}, "ref/net472/UnityEngine.InputLegacyModule.dll": {}, "ref/net472/UnityEngine.InputModule.dll": {}, "ref/net472/UnityEngine.JSONSerializeModule.dll": {}, "ref/net472/UnityEngine.LocalizationModule.dll": {}, "ref/net472/UnityEngine.ParticleSystemModule.dll": {}, "ref/net472/UnityEngine.PerformanceReportingModule.dll": {}, "ref/net472/UnityEngine.Physics2DModule.dll": {}, "ref/net472/UnityEngine.PhysicsModule.dll": {}, "ref/net472/UnityEngine.ProfilerModule.dll": {}, "ref/net472/UnityEngine.ScreenCaptureModule.dll": {}, "ref/net472/UnityEngine.SharedInternalsModule.dll": {}, "ref/net472/UnityEngine.SpriteMaskModule.dll": {}, "ref/net472/UnityEngine.SpriteShapeModule.dll": {}, "ref/net472/UnityEngine.StreamingModule.dll": {}, "ref/net472/UnityEngine.SubstanceModule.dll": {}, "ref/net472/UnityEngine.SubsystemsModule.dll": {}, "ref/net472/UnityEngine.TLSModule.dll": {}, "ref/net472/UnityEngine.TerrainModule.dll": {}, "ref/net472/UnityEngine.TerrainPhysicsModule.dll": {}, "ref/net472/UnityEngine.TextCoreModule.dll": {}, "ref/net472/UnityEngine.TextRenderingModule.dll": {}, "ref/net472/UnityEngine.TilemapModule.dll": {}, "ref/net472/UnityEngine.UI.dll": {}, "ref/net472/UnityEngine.UIElementsModule.dll": {}, "ref/net472/UnityEngine.UIModule.dll": {}, "ref/net472/UnityEngine.UNETModule.dll": {}, "ref/net472/UnityEngine.UmbraModule.dll": {}, "ref/net472/UnityEngine.UnityAnalyticsModule.dll": {}, "ref/net472/UnityEngine.UnityConnectModule.dll": {}, "ref/net472/UnityEngine.UnityTestProtocolModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestAssetBundleModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestAudioModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestTextureModule.dll": {}, "ref/net472/UnityEngine.UnityWebRequestWWWModule.dll": {}, "ref/net472/UnityEngine.VFXModule.dll": {}, "ref/net472/UnityEngine.VRModule.dll": {}, "ref/net472/UnityEngine.VehiclesModule.dll": {}, "ref/net472/UnityEngine.VideoModule.dll": {}, "ref/net472/UnityEngine.WindModule.dll": {}, "ref/net472/UnityEngine.XRModule.dll": {}, "ref/net472/UnityEngine.dll": {}, "ref/net472/com.rlabrecque.steamworks.net.dll": {}, "ref/net472/mscorlib.dll": {}}}, "Lib.Harmony/2.3.6": {"type": "package", "compile": {"lib/net472/0Harmony.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net472/0Harmony.dll": {"related": ".pdb;.xml"}}}}}, "libraries": {"Krafs.Publicizer/2.3.0": {"sha512": "DjktTgctwxUMhMkWKrRECer3LR1lHzanCOlE4mpinAiY8SfWJq4DG/QitP5h1A+WBjyWHzQSOG+204i3VpO1FA==", "type": "package", "path": "krafs.publicizer/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "build/Krafs.Publicizer.props", "build/Krafs.Publicizer.targets", "build/net472/Publicizer.dll", "build/net472/dnlib.dll", "contentfiles/cs/any/Publicizer/IgnoresAccessChecksToAttribute.cs", "icon.png", "krafs.publicizer.2.3.0.nupkg.sha512", "krafs.publicizer.nuspec"]}, "Krafs.Rimworld.Ref/1.5.4409": {"sha512": "0Sn9Ow0hbsQ1pmwHoKmcAzhMkv/BjHrIpZOH4eyUwHx238a3+2jDqNwCggKR8E3BouNeIOQuNcyMrc78slOtaA==", "type": "package", "path": "krafs.rimworld.ref/1.5.4409", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "icon.png", "krafs.rimworld.ref.1.5.4409.nupkg.sha512", "krafs.rimworld.ref.nuspec", "ref/net472/Assembly-CSharp-firstpass.dll", "ref/net472/Assembly-CSharp.dll", "ref/net472/ISharpZipLib.dll", "ref/net472/Mono.Posix.dll", "ref/net472/Mono.Security.dll", "ref/net472/NAudio.dll", "ref/net472/NVorbis.dll", "ref/net472/System.ComponentModel.Composition.dll", "ref/net472/System.Configuration.dll", "ref/net472/System.Core.dll", "ref/net472/System.Runtime.Serialization.dll", "ref/net472/System.Runtime.dll", "ref/net472/System.Security.dll", "ref/net472/System.ServiceModel.Internals.dll", "ref/net472/System.Xml.Linq.dll", "ref/net472/System.Xml.dll", "ref/net472/System.dll", "ref/net472/Unity.Burst.Unsafe.dll", "ref/net472/Unity.Burst.dll", "ref/net472/Unity.Mathematics.dll", "ref/net472/Unity.MemoryProfiler.dll", "ref/net472/Unity.TextMeshPro.dll", "ref/net472/UnityEngine.AIModule.dll", "ref/net472/UnityEngine.ARModule.dll", "ref/net472/UnityEngine.AccessibilityModule.dll", "ref/net472/UnityEngine.AndroidJNIModule.dll", "ref/net472/UnityEngine.AnimationModule.dll", "ref/net472/UnityEngine.AssetBundleModule.dll", "ref/net472/UnityEngine.AudioModule.dll", "ref/net472/UnityEngine.ClothModule.dll", "ref/net472/UnityEngine.ClusterInputModule.dll", "ref/net472/UnityEngine.ClusterRendererModule.dll", "ref/net472/UnityEngine.CoreModule.dll", "ref/net472/UnityEngine.CrashReportingModule.dll", "ref/net472/UnityEngine.DSPGraphModule.dll", "ref/net472/UnityEngine.DirectorModule.dll", "ref/net472/UnityEngine.GameCenterModule.dll", "ref/net472/UnityEngine.GridModule.dll", "ref/net472/UnityEngine.HotReloadModule.dll", "ref/net472/UnityEngine.IMGUIModule.dll", "ref/net472/UnityEngine.ImageConversionModule.dll", "ref/net472/UnityEngine.InputLegacyModule.dll", "ref/net472/UnityEngine.InputModule.dll", "ref/net472/UnityEngine.JSONSerializeModule.dll", "ref/net472/UnityEngine.LocalizationModule.dll", "ref/net472/UnityEngine.ParticleSystemModule.dll", "ref/net472/UnityEngine.PerformanceReportingModule.dll", "ref/net472/UnityEngine.Physics2DModule.dll", "ref/net472/UnityEngine.PhysicsModule.dll", "ref/net472/UnityEngine.ProfilerModule.dll", "ref/net472/UnityEngine.ScreenCaptureModule.dll", "ref/net472/UnityEngine.SharedInternalsModule.dll", "ref/net472/UnityEngine.SpriteMaskModule.dll", "ref/net472/UnityEngine.SpriteShapeModule.dll", "ref/net472/UnityEngine.StreamingModule.dll", "ref/net472/UnityEngine.SubstanceModule.dll", "ref/net472/UnityEngine.SubsystemsModule.dll", "ref/net472/UnityEngine.TLSModule.dll", "ref/net472/UnityEngine.TerrainModule.dll", "ref/net472/UnityEngine.TerrainPhysicsModule.dll", "ref/net472/UnityEngine.TextCoreModule.dll", "ref/net472/UnityEngine.TextRenderingModule.dll", "ref/net472/UnityEngine.TilemapModule.dll", "ref/net472/UnityEngine.UI.dll", "ref/net472/UnityEngine.UIElementsModule.dll", "ref/net472/UnityEngine.UIModule.dll", "ref/net472/UnityEngine.UNETModule.dll", "ref/net472/UnityEngine.UmbraModule.dll", "ref/net472/UnityEngine.UnityAnalyticsModule.dll", "ref/net472/UnityEngine.UnityConnectModule.dll", "ref/net472/UnityEngine.UnityTestProtocolModule.dll", "ref/net472/UnityEngine.UnityWebRequestAssetBundleModule.dll", "ref/net472/UnityEngine.UnityWebRequestAudioModule.dll", "ref/net472/UnityEngine.UnityWebRequestModule.dll", "ref/net472/UnityEngine.UnityWebRequestTextureModule.dll", "ref/net472/UnityEngine.UnityWebRequestWWWModule.dll", "ref/net472/UnityEngine.VFXModule.dll", "ref/net472/UnityEngine.VRModule.dll", "ref/net472/UnityEngine.VehiclesModule.dll", "ref/net472/UnityEngine.VideoModule.dll", "ref/net472/UnityEngine.WindModule.dll", "ref/net472/UnityEngine.XRModule.dll", "ref/net472/UnityEngine.dll", "ref/net472/com.rlabrecque.steamworks.net.dll", "ref/net472/mscorlib.dll"]}, "Lib.Harmony/2.3.6": {"sha512": "HqXPz33Z+R8ZDCLS5pJZthg33AMdF1bQXUbz2V7Neb9E+sMQOf4S1wquhM4pLIZFX7BZcOQubclbVreiM1+qmw==", "type": "package", "path": "lib.harmony/2.3.6", "files": [".nupkg.metadata", ".signature.p7s", "HarmonyLogo.png", "LICENSE", "README.md", "lib.harmony.2.3.6.nupkg.sha512", "lib.harmony.nuspec", "lib/net35/0Harmony.dll", "lib/net35/0Harmony.pdb", "lib/net35/0Harmony.xml", "lib/net452/0Harmony.dll", "lib/net452/0Harmony.pdb", "lib/net452/0Harmony.xml", "lib/net472/0Harmony.dll", "lib/net472/0Harmony.pdb", "lib/net472/0Harmony.xml", "lib/net48/0Harmony.dll", "lib/net48/0Harmony.pdb", "lib/net48/0Harmony.xml", "lib/net5.0/0Harmony.dll", "lib/net5.0/0Harmony.pdb", "lib/net5.0/0Harmony.xml", "lib/net6.0/0Harmony.dll", "lib/net6.0/0Harmony.pdb", "lib/net6.0/0Harmony.xml", "lib/net7.0/0Harmony.dll", "lib/net7.0/0Harmony.pdb", "lib/net7.0/0Harmony.xml", "lib/net8.0/0Harmony.dll", "lib/net8.0/0Harmony.pdb", "lib/net8.0/0Harmony.xml", "lib/net9.0/0Harmony.dll", "lib/net9.0/0Harmony.pdb", "lib/net9.0/0Harmony.xml", "lib/netcoreapp3.0/0Harmony.dll", "lib/netcoreapp3.0/0Harmony.pdb", "lib/netcoreapp3.0/0Harmony.xml", "lib/netcoreapp3.1/0Harmony.dll", "lib/netcoreapp3.1/0Harmony.pdb", "lib/netcoreapp3.1/0Harmony.xml", "lib/netstandard2.0/_._"]}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.7.2": ["Krafs.Publicizer >= 2.3.0", "Krafs.Rimworld.Ref >= 1.5.4409", "Lib.Harmony >= 2.3.6"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\Rimworld Modding\\Operation Homecoming\\Operation Homecoming.csproj", "projectName": "Operation Homecoming", "projectPath": "D:\\Projects\\Rimworld Modding\\Operation Homecoming\\Operation Homecoming.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\Rimworld Modding\\Operation Homecoming\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net472": {"projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net472": {"dependencies": {"Krafs.Publicizer": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.3.0, )"}, "Krafs.Rimworld.Ref": {"target": "Package", "version": "[1.5.4409, )"}, "Lib.Harmony": {"target": "Package", "version": "[2.3.6, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}
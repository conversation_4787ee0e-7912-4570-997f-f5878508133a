<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{16572FE6-6A96-49F6-861F-04E5C02FDC91}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Operation_Homecoming</RootNamespace>
    <AssemblyName>Operation Homecoming</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\..\SteamLibrary\steamapps\common\RimWorld\Mods\Operation Homecoming\Assemblies\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Core\CompKidnapContactTracker.cs" />
    <Compile Include="Core\CustomDefs.cs" />
    <Compile Include="Core\DefModExtensions.cs" />
    <Compile Include="Core\HarmonyPatches.cs" />
    <Compile Include="Core\KidnapCampSiteComp.cs" />
    <Compile Include="Core\ModSettings_OperationHomecoming.cs" />
    <Compile Include="Core\OHIncidentWorker_ReturnRaid.cs" />
    <Compile Include="Core\OHQuestUtility.cs" />
    <Compile Include="Core\OperationHomecoming.cs" />
    <Compile Include="Core\SignalHandlers.cs" />
    <Compile Include="Core\SitePartWorkers.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Krafs.Publicizer">
      <Version>2.3.0</Version>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Krafs.Rimworld.Ref">
      <Version>1.5.4409</Version>
    </PackageReference>
    <PackageReference Include="Lib.Harmony">
      <Version>2.3.6</Version>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Features\" />
    <Folder Include="Integrations\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
// ────────────────────────────────────────────────────────────────────────────────
// CustomDefs.cs  –  RimWorld 1.5+
// Custom Def classes for Operation Homecoming
// ────────────────────────────────────────────────────────────────────────────────

using Verse;

namespace OperationHomecoming.Core
{
    public class ContactFlavorDef : Def
    {
        // Def class for contact flavors
    }

    public class RumorLetterDef : Def
    {
        // Def class for rumor letters
    }

    public class HediffSetDef : Def
    {
        // Def class for hediff sets
    }

    public class SkillOffsetDef : Def
    {
        // Def class for skill offsets
    }
}
